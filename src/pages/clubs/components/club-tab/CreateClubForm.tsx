import { Button } from '@/components/ui/Button';
import { Form } from '@/components/ui/form/Form';
import { FormInput } from '@/components/ui/form/FormInput';
import { FormSelect, SelectItem } from '@/components/ui/form/FormSelect';
import { FormTextArea } from '@/components/ui/form/FormTextArea';
import { ClubCategoryEnum } from '@/generated/graphql';
import { z } from 'zod';
import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { FormImageUpload } from '@/components/ui/form/FormImageUpload';
import { ImageTooltip } from './ImageTooltip';

const ALLOWED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/svg+xml'] as const;
const MAX_IMAGE_SIZE_BYTES = 5 * 1024 * 1024;

function validateImageFile(
  imageFile: File | null | undefined,
  options: { required: boolean }
): string | null {
  if (options.required && !imageFile) return 'Image is required';
  if (!imageFile) return null;
  if (!(imageFile instanceof File)) return 'Please select a valid image file';
  if (imageFile.size > MAX_IMAGE_SIZE_BYTES) return 'File size must not exceed 5 MB.';
  if (!ALLOWED_IMAGE_TYPES.includes((imageFile.type || '') as any))
    return 'Only PNG, JPEG, and SVG are allowed';
  return null;
}

const CATEGORY_OPTIONS = [
  { value: ClubCategoryEnum.Creative, label: 'Creative' },
  { value: ClubCategoryEnum.FitnessOutdoor, label: 'Fitness & Outdoor' },
  { value: ClubCategoryEnum.FoodDrink, label: 'Food & Drink' },
  { value: ClubCategoryEnum.Hobbies, label: 'Hobbies' },
  { value: ClubCategoryEnum.SocialFamily, label: 'Social & Family' },
];

const clubFormSchema = z.object({
  name: z.string().min(1, 'Club name is required'),
  description: z.string().min(1, 'Description is required'),
  category: z
    .nativeEnum(ClubCategoryEnum, {
      required_error: 'Category is required',
      invalid_type_error: 'Please select a valid category',
    })
    .optional(),
  about: z
    .string()
    .optional()
    .refine((val) => !val || val.length <= 450, {
      message: 'About club must be less than 450 characters',
    }),
  image: z
    .any()
    .optional()
    .refine((val) => val === undefined || val === null || val instanceof File, {
      message: 'Please select a valid image file',
    }),
});

export type ClubFormSchema = z.infer<typeof clubFormSchema>;

// Extended type that includes image
export type ClubFormData = ClubFormSchema & {
  image?: File | null;
  uploadedImage?: { id: string; url: string } | null;
};

interface ClubFormProps {
  onConfirm: (data: ClubFormData) => void;
  onCancel: () => void;
  onFormChange?: (data: ClubFormData, isValid: boolean, isDirty: boolean) => void;
  initialValues?: Partial<ClubFormSchema> & { img?: { url?: string } };
  isEdit?: boolean;
  isLoading?: boolean;
  hideButtons?: boolean;
}

const CreateClubForm = ({
  onConfirm,
  onCancel,
  onFormChange,
  initialValues = {},
  isEdit = false,
  isLoading = false,
  hideButtons = false,
}: ClubFormProps) => {
  // Track form changes and validation
  const FormTracker = () => {
    const {
      watch,
      formState: { isValid, dirtyFields },
    } = useFormContext();
    const formData = watch() as ClubFormSchema;
    const isDirty = Object.keys(dirtyFields).length > 0;

    useEffect(() => {
      if (!onFormChange) return;
      const formDataWithImage: ClubFormData = {
        ...formData,
        image: (formData as any)?.image ?? null,
      };
      onFormChange(formDataWithImage, isValid, isDirty);
    }, [formData, isValid, isDirty]);

    return null;
  };

  const onSubmit = async (data: ClubFormSchema) => {
    const formDataWithImage: ClubFormData = {
      ...data,
      image: (data as any)?.image ?? null,
    };

    onConfirm(formDataWithImage);
  };

  const defaultValues: ClubFormSchema = useMemo(() => {
    return {
      name: initialValues.name || '',
      description: initialValues.description || '',
      category: initialValues.category || undefined,
      about: initialValues.about || '',
    };
  }, [initialValues]);

  return (
    <Form
      id='club-form'
      key={`${JSON.stringify(defaultValues)}`}
      mode='onChange'
      showUnsavedChangesConfirmModal={isEdit}
      schema={useMemo(
        () =>
          clubFormSchema.superRefine((values, ctx) => {
            // Category validation - required
            if (!values.category) {
              ctx.addIssue({ code: 'custom', message: 'Category is required', path: ['category'] });
            }

            // Image validation
            const imageValue = (values as any)?.image as File | null | undefined;
            const hasExistingImage = isEdit && !!initialValues?.img?.url;
            const userExplicitlyRemoved = hasExistingImage && imageValue === null;
            const imageError = validateImageFile(imageValue, {
              // Require image if:
              // - there is no existing image (create mode), or
              // - user explicitly removed the existing image without replacement
              required: !hasExistingImage || userExplicitlyRemoved,
            });
            if (imageError) ctx.addIssue({ code: 'custom', message: imageError, path: ['image'] });
          }),
        [isEdit, initialValues?.img?.url]
      )}
      onSubmit={onSubmit}
      defaultValues={defaultValues}
      className='space-y-6'
    >
      <FormTracker />
      {/* Image Upload */}
      <div className='flex flex-col sm:flex-row t items-start sm:space-x-16 space-y-2 sm:space-y-0 pb-6 border-b border-gray-200'>
        <label className='text-sm flex gap-2 font-medium text-gray-700 w-full sm:w-32 sm:flex-shrink-0 pt-0 sm:pt-3 text-left'>
          <p>
            {' '}
            Image<span className='text-red-500'>*</span>
          </p>
          <ImageTooltip />
        </label>
        <div className='w-full sm:flex-1 sm:max-w-[512px] image-upload-responsive'>
          <FormImageUpload
            name='image'
            initialUrl={initialValues?.img?.url || null}
            disabled={isLoading}
            placeholder='Upload Image'
          />
        </div>
      </div>

      {/* Name */}
      <div className='flex flex-col sm:flex-row items-start sm:space-x-16 space-y-2 sm:space-y-0 pb-6 border-b border-gray-200'>
        <label className='text-sm font-medium text-gray-700 w-full sm:w-32 sm:flex-shrink-0 text-left'>
          Name<span className='text-red-500'>*</span>
        </label>
        <div className='w-full sm:flex-1 sm:max-w-[512px]'>
          <FormInput
            name='name'
            placeholder='Give your club a name'
            className='w-full text-sm md:text-base placeholder:text-sm md:placeholder:text-base placeholder:text-gray-500'
            formItemClassName='space-y-1'
            formLabelClassName='hidden'
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Description */}
      <div className='flex flex-col sm:flex-row items-start sm:space-x-16 space-y-2 sm:space-y-0 pb-6 border-b border-gray-200'>
        <label className='text-sm font-medium text-gray-700 w-full sm:w-32 sm:flex-shrink-0 text-left'>
          Description<span className='text-red-500'>*</span>
        </label>
        <div className='w-full sm:flex-1 sm:max-w-[512px]'>
          <FormInput
            name='description'
            placeholder='Provide club tagline'
            className='w-full text-sm md:text-base placeholder:text-sm md:placeholder:text-base placeholder:text-gray-500'
            formItemClassName='space-y-1'
            formLabelClassName='hidden'
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Category */}
      <div className='flex flex-col sm:flex-row items-start sm:space-x-16 space-y-2 sm:space-y-0 pb-6 border-b border-gray-200'>
        <label className='text-sm font-medium text-gray-700 w-full sm:w-32 sm:flex-shrink-0 text-left'>
          Category<span className='text-red-500'>*</span>
        </label>
        <div className='w-full sm:flex-1 sm:max-w-[512px]'>
          <FormSelect
            className='h-11 w-full text-sm md:text-base [&>span]:text-sm md:[&>span]:text-base'
            name='category'
            placeholder='Club category'
            disabled={isLoading}
          >
            {CATEGORY_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </FormSelect>
        </div>
      </div>

      {/* About Club */}
      <div className='flex flex-col sm:flex-row items-start sm:space-x-16 space-y-2 sm:space-y-0 pb-6 '>
        <label className='text-sm font-medium text-gray-700 w-full sm:w-32 sm:flex-shrink-0 pt-0 sm:pt-3 text-left'>
          About Club
        </label>
        <div className='w-full sm:flex-1 sm:max-w-[512px]'>
          <FormTextArea
            className='w-full text-sm md:text-base placeholder:text-sm md:placeholder:text-base'
            name='about'
            placeholder='Provide detailed club description (max 450 characters)'
            rows={4}
            formItemClassName='space-y-1'
            formLabelClassName='hidden'
            disabled={isLoading}
          />
        </div>
      </div>

      {/* Action Buttons */}
      {!hideButtons && (
        <div className='flex justify-end space-x-3 pt-4'>
          <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            type='submit'
            disabled={isLoading}
            loading={isLoading}
            disableOnInvalid
            disabledOnNotDirty
          >
            {isEdit ? 'Update club' : 'Create club'}
          </Button>
        </div>
      )}
    </Form>
  );
};

export default CreateClubForm;
