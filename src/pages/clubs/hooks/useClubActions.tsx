import { ApolloError, useApolloClient } from '@apollo/client';
import { useToast } from '@/hooks/useToast';
import { ClubTemplate, ClubTemplatesDocument } from '@/generated/graphql';
import { useCallback, useState } from 'react';
import {
  useCreateClubTemplateMutation,
  useUpdateClubTemplateMutation,
  useDeleteClubTemplateMutation,
} from '@/generated/graphql';
import { TOAST_DURATION } from '@/lib/constants';

export const useClubActions = () => {
  const client = useApolloClient();
  const { toast } = useToast();
  const [selectedClub, setSelectedClub] = useState<ClubTemplate | null>(null);
  const [isDeleteClubModalOpen, setIsDeleteClubModalOpen] = useState(false);
  const [isEditClubModalOpen, setIsEditClubModalOpen] = useState(false);
  const [isCreateClubModalOpen, setIsCreateClubModalOpen] = useState(false);

  const [createClubTemplate, { loading: isCreatingClub }] = useCreateClubTemplateMutation();
  const [updateClubTemplate, { loading: isUpdatingClub }] = useUpdateClubTemplateMutation();
  const [deleteClubTemplate, { loading: isDeletingClub }] = useDeleteClubTemplateMutation();

  // Handle API errors uniformly
  const handleApiError = useCallback(
    (error: unknown, errorMessage = 'Operation failed') => {
      if (error instanceof ApolloError) {
        toast({
          variant: 'destructive',
          title: error.graphQLErrors[0]?.message || errorMessage,
          duration: TOAST_DURATION,
        });
      }
    },
    [toast]
  );

  // Success toast helper
  const showSuccess = useCallback(
    (message: string) => {
      toast({ variant: 'success', title: message, duration: TOAST_DURATION });
    },
    [toast]
  );

  const handleOpenCreateClubModal = useCallback(() => {
    setIsCreateClubModalOpen(true);
  }, []);

  const handleOpenEditClubModal = useCallback((club: ClubTemplate) => {
    setSelectedClub(club);
    setIsEditClubModalOpen(true);
  }, []);

  const handleOpenDeleteClubModal = useCallback((club: ClubTemplate) => {
    setSelectedClub(club);
    setIsDeleteClubModalOpen(true);
  }, []);

  const handleConfirmCreateClub = useCallback(
    async (data: any) => {
      try {
        // Extract image, uploadedImage and about from data
        const { image: _image, uploadedImage, about, ...input } = data;

        // Prepare input for API
        const apiInput = {
          ...input,
          about: about || '', // Ensure about is included
          imgId: uploadedImage?.id, // Include uploaded image ID
        };

        await createClubTemplate({
          variables: { input: apiInput },
        });

        showSuccess('Club created successfully');
      } catch (error) {
        handleApiError(error, 'Failed to create club');
      }
    },
    [createClubTemplate, handleApiError, showSuccess]
  );

  const handleConfirmUpdateClub = useCallback(
    async (input: any) => {
      try {
        await updateClubTemplate({ variables: { input } });
        setIsEditClubModalOpen(false);
        showSuccess('Club updated successfully');
      } catch (error) {
        handleApiError(error, 'Failed to update club');
      }
    },
    [updateClubTemplate, handleApiError, showSuccess]
  );

  const handleConfirmDeleteClub = useCallback(
    async (clubId: string) => {
      try {
        await deleteClubTemplate({ variables: { deleteClubTemplateId: clubId } });
        client.refetchQueries({ include: [ClubTemplatesDocument] });
        setIsDeleteClubModalOpen(false);
        showSuccess('Club deleted successfully');
      } catch (error) {
        handleApiError(error, 'Failed to delete club');
      }
    },
    [deleteClubTemplate, client, handleApiError, showSuccess]
  );

  return {
    isDeleteClubModalOpen,
    isEditClubModalOpen,
    isCreateClubModalOpen,
    isCreatingClub,
    isUpdatingClub,
    isDeletingClub,
    selectedClub,
    setIsDeleteClubModalOpen,
    setIsEditClubModalOpen,
    setIsCreateClubModalOpen,
    handleOpenCreateClubModal,
    handleOpenEditClubModal,
    handleOpenDeleteClubModal,
    handleConfirmCreateClub,
    handleConfirmUpdateClub,
    handleConfirmDeleteClub,
  };
};
