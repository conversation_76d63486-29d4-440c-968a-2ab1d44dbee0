import { Button } from '@/components/ui/Button';
import { ClubFormData } from '@/pages/clubs/components/club-tab/CreateClubForm';
import { useClubTemplateByIdQuery } from '@/generated/graphql';
import { useClubActions } from '@/pages/clubs/hooks/useClubActions';
import { useImageUpload } from '@/pages/clubs/hooks/useImageUpload';
import { AppRoutePaths } from '@/lib/constants';
import { useNavigate, useParams } from 'react-router-dom';
import { useMemo, useState } from 'react';
import CreateClubForm from '@/pages/clubs/components/club-tab/CreateClubForm';

export function ClubTemplateFormPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  const [isLoading, setIsLoading] = useState(false);

  const clubActions = useClubActions();
  const { uploadImage } = useImageUpload();

  const { data, loading: isLoadingClub } = useClubTemplateByIdQuery({
    variables: { clubTemplateByIdId: id as string },
    skip: !isEdit,
  });

  const [isFormValid, setIsFormValid] = useState(false);
  const [isFormDirty, setIsFormDirty] = useState(false);

  // Build initial values from fetched club template
  const initialValues = useMemo(() => {
    if (!isEdit) return {};
    const club = data?.clubTemplateById;
    if (!club) return {};
    return {
      name: club.name,
      description: club.description || '',
      category: club.category || undefined,
      about: club.about || '',
      img: club.img ? { url: club.img.url || '' } : undefined,
    } as const;
  }, [isEdit, data?.clubTemplateById]);

  async function handleSubmit(values: ClubFormData) {
    try {
      setIsLoading(true);
      let uploadedImage = null as ClubFormData['uploadedImage'] | null;

      if (values.image) {
        uploadedImage = await uploadImage(values.image);
        if (!uploadedImage) return;
      }

      if (isEdit && id) {
        const updateData = {
          id,
          name: values.name,
          description: values.description,
          category: values.category,
          ...(values.about !== undefined && { about: values.about }),
          ...(uploadedImage?.id && { imgId: uploadedImage.id }),
        };
        await clubActions.handleConfirmUpdateClub(updateData);
      } else {
        await clubActions.handleConfirmCreateClub({ ...values, uploadedImage });
      }

      navigate(-1);
    } catch (_) {
      // Error toasts handled in hooks
    } finally {
      setIsLoading(false);
    }
  }

  function handleCancel() {
    navigate(-1);
  }

  const title = isEdit ? 'Edit club' : 'New club';
  const subtitle = 'Update club details here.';
  const submitLabel = isEdit ? 'Update club' : 'Create club';

  return (
    <div className='w-full flex py-4 pt-8 space-y-4 flex-col flex-1 px-4 sm:px-12 '>
      <div className='flex items-start pb-6 w-full  justify-between border-b border-gray-200'>
        <div>
          <h1 className='text-lg text-gray-900 font-semibold'>{title}</h1>
          <p className='text-sm mt-1'>{subtitle}</p>
        </div>
        <div className='flex space-x-3'>
          <Button variant='outline' onClick={handleCancel} className='text-sm'>
            Cancel
          </Button>
          <Button
            loading={isLoading}
            disabled={isLoading || !isFormValid || !isFormDirty}
            form='club-form'
            className='text-sm'
            type='submit'
          >
            {submitLabel}
          </Button>
        </div>
      </div>

      <div className='mt-6'>
        <CreateClubForm
          onCancel={handleCancel}
          onConfirm={handleSubmit}
          onFormChange={(_, isValid, isDirty) => {
            setIsFormValid(isValid);
            setIsFormDirty(isDirty);
          }}
          initialValues={initialValues}
          isEdit={isEdit}
          isLoading={isLoadingClub || isLoading}
          hideButtons={true}
        />
      </div>
    </div>
  );
}

export default ClubTemplateFormPage;
